/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Main exports for the LangChain-based Gemini CLI implementation

// Configuration
export { LangChainConfig } from './config/config.js';
export type { 
  LangChainContentGeneratorConfig, 
  AgentState,
  LangChainToolWrapper 
} from './types/index.js';

// Core components
export { LangChainContentGenerator } from './core/contentGenerator.js';
export { StateGraphAgent } from './core/stateGraphAgent.js';
export { SessionManager } from './core/sessionManager.js';
export type { SessionData } from './core/sessionManager.js';

// Model factory
export {
  createChatModel,
  createEmbeddings,
  getModelDisplayName,
  validateModelName,
  getDefaultModel,
} from './core/modelFactory.js';

// Tools
export { 
  CoreToolWrapper, 
  LangChainToolRegistry 
} from './tools/toolRegistry.js';

// Re-export important types from core
export type {
  AuthType,
  ApprovalMode,
} from './types/index.js';

// Re-export CLI integration
export {
  shouldUseLangChain,
  createCLIInstance,
  getLangChainSettings,
  setupLangChainEnvironment,
} from './cli-integration.js';

import { logger } from './utils/logger.js';

// Global session mapping for interactive mode
const interactiveSessionMap = new Map<string, string>();

/**
 * Create a complete LangChain-based Gemini CLI instance
 */
export async function createLangChainGeminiCLI(params: import('@google/gemini-cli-core').ConfigParameters & {
  baseURL?: string;
  authType?: import('./types/index.js').ExtendedAuthType;
}) {
  // Create configuration
  const { LangChainConfig } = await import('./config/config.js');
  const config = new LangChainConfig(params);
  await config.initialize();
  
  // Create session manager with targetDir
  const { SessionManager } = await import('./core/sessionManager.js');
  const sessionManager = new SessionManager(
    100, // maxSessions
    24 * 60 * 60 * 1000, // sessionTimeout (24 hours)
    true, // persistenceEnabled
  );
  
  // Create agent - StateGraphAgent
  const { StateGraphAgent } = await import('./core/stateGraphAgent.js');
  const agent = new StateGraphAgent(
    config,
    '',
    { enablePersistence: true }
  );
  
  return {
    config,
    sessionManager,
    agent,
    
    // Convenience methods
    async processMessage(
      userMessage: string,
      sessionId?: string,
      userMemory?: string
    ): Promise<string> {
      const id = sessionId || sessionManager.createSession(userMemory);
      const history = sessionManager.getConversationHistory(id);

      // Add user message to session before processing
      const { HumanMessage, AIMessage } = await import('@langchain/core/messages');
      sessionManager.addMessage(id, new HumanMessage(userMessage));

      // Get the initial message count to track new messages
      const initialMessageCount = history.length + 1; // +1 for the user message we just added

      // Process the message
      const response = await agent.processMessage(userMessage, id, userMemory, history);

      // Get the final state from the agent's graph to capture all intermediate messages
      const finalState = await agent.getGraph().getState({ configurable: { thread_id: id } });

      // Extract new messages that were generated during processing (tool calls, tool results, etc.)
      if (finalState && finalState.values && finalState.values.messages) {
        const allMessages = finalState.values.messages;
        const newMessages = allMessages.slice(initialMessageCount);

        // Add all new messages to session (this includes tool calls and tool results)
        if (newMessages.length > 0) {
          sessionManager.updateSession(id, newMessages);
        }
      } else {
        // Fallback: just add the final AI response if we can't get the full state
        if (response.trim()) {
          sessionManager.addMessage(id, new AIMessage(response));
        }
      }

      return response;
    },
    
    async *streamMessage(
      userMessage: string,
      sessionId?: string,
      userMemory?: string
    ): AsyncGenerator<string> {
      let id: string;

      if (sessionId) {
        // Check if we have a mapped session for this interactive session
        if (interactiveSessionMap.has(sessionId)) {
          id = interactiveSessionMap.get(sessionId)!;
        } else {
          // Create a new session and map it to the interactive session ID
          id = sessionManager.createSession(userMemory);
          interactiveSessionMap.set(sessionId, id);
          logger.debug(`[CLI] Mapped interactive session ${sessionId} to actual session ${id}`);
        }
      } else {
        id = sessionManager.createSession(userMemory);
      }

      const history = sessionManager.getConversationHistory(id);

      // Add user message to session before processing
      sessionManager.addMessage(id, new (await import('@langchain/core/messages')).HumanMessage(userMessage));

      // Get the initial message count to track new messages
      const initialMessageCount = history.length + 1; // +1 for the user message we just added

      let responseContent = '';

      // Stream the response and collect it
      for await (const chunk of agent.streamMessage(userMessage, id, userMemory, history)) {
        responseContent += chunk;
        yield chunk;
      }

      // Get the final state from the agent's graph to capture all intermediate messages
      try {
        const finalState = await agent.getGraph().getState({ configurable: { thread_id: id } });

        // Extract new messages that were generated during processing (tool calls, tool results, etc.)
        if (finalState && finalState.values && finalState.values.messages) {
          const allMessages = finalState.values.messages;
          const newMessages = allMessages.slice(initialMessageCount);

          // Add all new messages to session (this includes tool calls and tool results)
          if (newMessages.length > 0) {
            sessionManager.updateSession(id, newMessages);
          }
        } else {
          // Fallback: just add the final AI response if we can't get the full state
          if (responseContent.trim()) {
            sessionManager.addMessage(id, new (await import('@langchain/core/messages')).AIMessage(responseContent));
          }
        }
      } catch (error) {
        logger.error('[CLI] Failed to get final state from graph:', error);
        // Fallback: just add the final AI response
        if (responseContent.trim()) {
          sessionManager.addMessage(id, new (await import('@langchain/core/messages')).AIMessage(responseContent));
        }
      }
    },
  };
}
